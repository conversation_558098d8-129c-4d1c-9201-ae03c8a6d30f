{"snapshots": {"/Users/<USER>/Flutter-Projects/Ajory/New School/parent-mobile-app/lib/src/pages/web_view_page.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/New School/parent-mobile-app/lib/src/pages/web_view_page.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_inappwebview/flutter_inappwebview.dart';\nimport 'package:provider/provider.dart';\nimport 'package:url_launcher/url_launcher.dart';\nimport 'package:up_school_parent/src/core/utils/one_signal_service.dart';\nimport 'package:up_school_parent/src/core/utils/app_constants.dart';\nimport 'package:up_school_parent/src/pages/services/download_methods.dart';\nimport 'package:up_school_parent/src/pages/widgets/no_internet_connection_widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\nimport '../core/config/app_config.dart';\n\nclass WebViewPage extends StatefulWidget {\n  const WebViewPage({super.key});\n\n  @override\n  State<WebViewPage> createState() => _WebViewPageState();\n}\n\nclass _WebViewPageState extends State<WebViewPage> {\n\n  @override\n  void initState() {\n    super.initState();\n    // Force reload when the widget is initialized\n    WidgetsBinding.instance.addPostFrameCallback((_) {\n      _forceReload();\n    });\n  }\n\n  // Force reload the WebView to get latest content\n  Future<void> _forceReload() async {\n    if (webViewController != null) {\n      await webViewController!.reload();\n    }\n  }\n\n\n\n  // Function to handle external links and open them in the browser\n  Future<void> _launchExternalLink(String url) async {\n    final Uri uri = Uri.parse(url);  // Convert the string URL to a Uri object\n    if (await canLaunchUrl(uri)) { \n      await launchUrl(uri, mode: LaunchMode.externalApplication);  // Open in external browser\n    }\n  }\n\n   void onDownloadStart(\n    InAppWebViewController controller,\n    DownloadStartRequest request,\n  ) async {\n    try {\n      final lang = await controller.evaluateJavascript(\n          source: \"window.localStorage.getItem('UPSPlang')\");\n      final isArabic = lang.toString().contains(\"ar\");\n\n      // Use the State's context\n      context.showBarMessage(isArabic ? \"...جاري الفتح\" : \"מתחיל הורדה...\");\n\n      await downloadFiles(context, controller: controller);\n    } catch (e) {\n      Log.e('DownloadError: $e');\n    }\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: SafeArea(\n        child: Consumer<AppConfig>(\n          builder: (context, appConfig, child) {\n            if (!appConfig.hasInternet) {\n              return const NoInternetConnectionWidget();\n            }\n\n            final token = OneSignalNotificationService.getUserId();\n\n            return InAppWebView(\n              onWebViewCreated: appConfig.onWebViewCreated,\n              onLoadStop: (controller, url) async {\n                await appConfig.addTokenToLogin(controller: controller);\n              },\n              onProgressChanged: (controller, progress) async {\n                if (progress == 100) {\n                  await appConfig.addTokenToLogin(controller: controller);\n                }\n              },\n              onUpdateVisitedHistory: (controller, url, androidIsReload) async {\n                await appConfig.addTokenToLogin(controller: controller);\n              },\n              onDownloadStartRequest: onDownloadStart,\n              onReceivedServerTrustAuthRequest: (controller, challenge) async {\n                return ServerTrustAuthResponse(\n                    action: ServerTrustAuthResponseAction.PROCEED);\n              },\n              initialUrlRequest: URLRequest(\n                url: WebUri.uri(\n                  Uri.parse(\n                    '${AppConstants.appUrl}?token=$token&v=${DateTime.now().millisecondsSinceEpoch}',\n                  ),\n                ),\n              ),\n              onLoadStart: (controller, url) async {\n                // Check if the link is external\n                if (url?.toString().startsWith('https://upschool.org.il') == true) {\n                  // Launch the external link in the browser\n                  _launchExternalLink(url.toString());\n                  controller.stopLoading();  // Prevent WebView from opening it\n                }\n              },\n            );\n          },\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1758011410594, "deltas": [{"timestamp": 1758011412753, "changes": [{"type": "DELETE", "lineNumber": 37, "oldContent": ""}]}, {"timestamp": 1758011415053, "changes": [{"type": "INSERT", "lineNumber": 19, "content": "  "}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  void initState() {"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "    super.initState();"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "    // Force reload when the widget is initialized"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "    WidgetsBinding.instance.addPostFrameCallback((_) {"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "      _forceReload();"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "    });"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 28, "oldContent": ""}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  // Force reload the WebView to get latest content"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  Future<void> _forceReload() async {"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "    if (webViewController != null) {"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "      await webViewController!.reload();"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 35, "oldContent": ""}, {"type": "DELETE", "lineNumber": 36, "oldContent": ""}]}, {"timestamp": 1758011433506, "changes": [{"type": "MODIFY", "lineNumber": 19, "content": "  InAppWebViewController? webViewController;", "oldContent": "  "}, {"type": "INSERT", "lineNumber": 21, "content": "  @override"}, {"type": "INSERT", "lineNumber": 22, "content": "  void initState() {"}, {"type": "INSERT", "lineNumber": 23, "content": "    super.initState();"}, {"type": "INSERT", "lineNumber": 24, "content": "    // Force reload when the widget is initialized"}, {"type": "INSERT", "lineNumber": 25, "content": "    WidgetsBinding.instance.addPostFrameCallback((_) {"}, {"type": "INSERT", "lineNumber": 26, "content": "      _forceReload();"}, {"type": "INSERT", "lineNumber": 27, "content": "    });"}, {"type": "INSERT", "lineNumber": 28, "content": "  }"}, {"type": "INSERT", "lineNumber": 29, "content": ""}, {"type": "INSERT", "lineNumber": 30, "content": "  // Force reload the WebView to get latest content"}, {"type": "INSERT", "lineNumber": 31, "content": "  Future<void> _forceReload() async {"}, {"type": "INSERT", "lineNumber": 32, "content": "    if (webViewController != null) {"}, {"type": "INSERT", "lineNumber": 33, "content": "      await webViewController!.reload();"}, {"type": "INSERT", "lineNumber": 34, "content": "    }"}, {"type": "INSERT", "lineNumber": 35, "content": "  }"}, {"type": "INSERT", "lineNumber": 36, "content": ""}, {"type": "INSERT", "lineNumber": 37, "content": ""}]}, {"timestamp": 1758011446422, "changes": [{"type": "DELETE", "lineNumber": 22, "oldContent": "  // Function to handle external links and open them in the browser"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  Future<void> _launchExternalLink(String url) async {"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "    final Uri uri = Uri.parse(url);  // Convert the string URL to a Uri object"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "    if (await canLaunchUrl(uri)) { "}, {"type": "DELETE", "lineNumber": 30, "oldContent": "      await launchUrl(uri, mode: LaunchMode.externalApplication);  // Open in external browser"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "    }"}, {"type": "INSERT", "lineNumber": 29, "content": ""}, {"type": "INSERT", "lineNumber": 30, "content": "  // Force reload the WebView to get latest content"}, {"type": "INSERT", "lineNumber": 31, "content": "  Future<void> _forceReload() async {"}, {"type": "INSERT", "lineNumber": 32, "content": "    if (webViewController != null) {"}, {"type": "INSERT", "lineNumber": 33, "content": "      await webViewController!.reload();"}, {"type": "INSERT", "lineNumber": 34, "content": "    }"}, {"type": "INSERT", "lineNumber": 37, "content": "  // Function to handle external links and open them in the browser"}, {"type": "INSERT", "lineNumber": 38, "content": "  Future<void> _launchExternalLink(String url) async {"}, {"type": "INSERT", "lineNumber": 39, "content": "    final Uri uri = Uri.parse(url); // Convert the string URL to a Uri object"}, {"type": "INSERT", "lineNumber": 40, "content": "    if (await canLaunchUrl(uri)) {"}, {"type": "INSERT", "lineNumber": 41, "content": "      await launchUrl(uri,"}, {"type": "INSERT", "lineNumber": 42, "content": "          mode: LaunchMode.externalApplication); // Open in external browser"}, {"type": "INSERT", "lineNumber": 43, "content": "    }"}, {"type": "INSERT", "lineNumber": 44, "content": "  }"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "   void onDownloadStart("}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  // Force reload the WebView to get latest content"}, {"type": "INSERT", "lineNumber": 46, "content": "  void onDownloadStart("}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  Future<void> _forceReload() async {"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "    if (webViewController != null) {"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "      await webViewController!.reload();"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 51, "oldContent": ""}, {"type": "DELETE", "lineNumber": 54, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 103, "content": "                if (url?.toString().startsWith('https://upschool.org.il') ==", "oldContent": "                if (url?.toString().startsWith('https://upschool.org.il') == true) {"}, {"type": "INSERT", "lineNumber": 104, "content": "                    true) {"}, {"type": "MODIFY", "lineNumber": 107, "content": "                  controller.stopLoading(); // Prevent WebView from opening it", "oldContent": "                  controller.stopLoading();  // Prevent WebView from opening it"}]}, {"timestamp": 1758012274643, "changes": [{"type": "DELETE", "lineNumber": 25, "oldContent": ""}, {"type": "INSERT", "lineNumber": 26, "content": "      _forceReload();"}, {"type": "INSERT", "lineNumber": 27, "content": "    });"}, {"type": "INSERT", "lineNumber": 28, "content": "  }"}, {"type": "INSERT", "lineNumber": 29, "content": ""}, {"type": "DELETE", "lineNumber": 29, "oldContent": "      _forceReload();"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "    });"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 38, "oldContent": ""}, {"type": "DELETE", "lineNumber": 42, "oldContent": "    InAppWebViewController controller,"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "    DownloadStartRequest request,"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "  ) async {"}, {"type": "INSERT", "lineNumber": 45, "content": ""}, {"type": "INSERT", "lineNumber": 47, "content": "    InAppWebViewController controller,"}, {"type": "INSERT", "lineNumber": 48, "content": "    DownloadStartRequest request,"}, {"type": "INSERT", "lineNumber": 49, "content": "  ) async {"}, {"type": "MODIFY", "lineNumber": 77, "content": "              onWebViewCreated: (controller) async {", "oldContent": "              onWebViewCreated: appConfig.onWebViewCreated,"}, {"type": "INSERT", "lineNumber": 78, "content": "                webViewController = controller;"}, {"type": "INSERT", "lineNumber": 79, "content": "                await appConfig.onWebViewCreated(controller);"}, {"type": "INSERT", "lineNumber": 80, "content": ""}, {"type": "INSERT", "lineNumber": 81, "content": "                // Configure cache settings to disable caching"}, {"type": "INSERT", "lineNumber": 82, "content": "                await controller.setSettings("}, {"type": "INSERT", "lineNumber": 83, "content": "                    settings: InAppWebViewSettings("}, {"type": "INSERT", "lineNumber": 84, "content": "                      cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 85, "content": "                      clearCache: true,"}, {"type": "INSERT", "lineNumber": 86, "content": "                      clearSessionCache: true,"}, {"type": "INSERT", "lineNumber": 87, "content": "                      incognito: true,"}, {"type": "INSERT", "lineNumber": 88, "content": "                    ));"}, {"type": "INSERT", "lineNumber": 89, "content": ""}, {"type": "INSERT", "lineNumber": 90, "content": "                // Force reload to get latest content"}, {"type": "INSERT", "lineNumber": 91, "content": "                await _forceReload();"}, {"type": "INSERT", "lineNumber": 92, "content": "              },"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "                  controller.stopLoading();  // Prevent WebView from opening it"}, {"type": "INSERT", "lineNumber": 123, "content": "                }"}]}, {"timestamp": 1758012291997, "changes": [{"type": "MODIFY", "lineNumber": 26, "content": "      _forceReload();", "oldContent": "      _forceReload();"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  // Force reload the WebView to get latest content"}, {"type": "MODIFY", "lineNumber": 30, "content": "  // Force reload the WebView to get latest content", "oldContent": "  Future<void> _forceReload() async {"}, {"type": "INSERT", "lineNumber": 31, "content": "  Future<void> _forceReload() async {"}, {"type": "DELETE", "lineNumber": 43, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 45, "content": "", "oldContent": "    InAppWebViewController controller,"}, {"type": "INSERT", "lineNumber": 46, "content": "  void onDownloadStart("}, {"type": "INSERT", "lineNumber": 47, "content": "    InAppWebViewController controller,"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "  void onDownloadStart("}, {"type": "DELETE", "lineNumber": 79, "oldContent": "              onLoadStop: (controller, url) async {"}, {"type": "DELETE", "lineNumber": 81, "oldContent": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "              },"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "              onProgressChanged: (controller, progress) async {"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "                if (progress == 100) {"}, {"type": "INSERT", "lineNumber": 84, "content": "                  cacheEnabled: false,"}, {"type": "INSERT", "lineNumber": 85, "content": "                  clearCache: true,"}, {"type": "INSERT", "lineNumber": 86, "content": "                  clearSessionCache: true,"}, {"type": "INSERT", "lineNumber": 87, "content": "                  incognito: true,"}, {"type": "INSERT", "lineNumber": 88, "content": "                ));"}, {"type": "INSERT", "lineNumber": 89, "content": ""}, {"type": "INSERT", "lineNumber": 90, "content": "                // Force reload to get latest content"}, {"type": "INSERT", "lineNumber": 91, "content": "                await _forceReload();"}, {"type": "INSERT", "lineNumber": 92, "content": "              },"}, {"type": "INSERT", "lineNumber": 93, "content": "              onLoadStop: (controller, url) async {"}, {"type": "INSERT", "lineNumber": 94, "content": "                await appConfig.addTokenToLogin(controller: controller);"}, {"type": "INSERT", "lineNumber": 95, "content": "              },"}, {"type": "INSERT", "lineNumber": 96, "content": "              onProgressChanged: (controller, progress) async {"}, {"type": "INSERT", "lineNumber": 97, "content": "                if (progress == 100) {"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "                      cacheEnabled: false,"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "                      clearCache: true,"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "                      clearSessionCache: true,"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "                      incognito: true,"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "                    ));"}, {"type": "DELETE", "lineNumber": 100, "oldContent": ""}, {"type": "DELETE", "lineNumber": 102, "oldContent": "                // Force reload to get latest content"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "                await _forceReload();"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "              },"}, {"type": "INSERT", "lineNumber": 123, "content": "                }"}, {"type": "DELETE", "lineNumber": 132, "oldContent": "                }"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/New School/parent-mobile-app/lib/src/core/config/app_config.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/New School/parent-mobile-app/lib/src/core/config/app_config.dart", "baseContent": "import 'dart:async';\nimport 'dart:developer';\n\nimport 'package:flutter/material.dart';\n// import 'package:webview_flutter/webview_flutter.dart';\nimport 'package:flutter_inappwebview/flutter_inappwebview.dart';\nimport 'package:internet_connection_checker/internet_connection_checker.dart';\nimport 'package:restart_app/restart_app.dart';\nimport 'package:up_school_parent/src/core/utils/app_constants.dart';\nimport 'package:up_school_parent/src/core/utils/one_signal_service.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass AppConfig extends ChangeNotifier {\n  //? Loading\n  bool isLoading = true;\n\n  set loading(bool value) {\n    isLoading = value;\n    notifyListeners();\n  }\n\n  //? Check Internet Connection\n  bool hasInternet = true;\n\n  //? Initialize App\n  Future<void> init() async {\n    try {\n      loading = true;\n\n      // await AppConfig.addFcmTokenToUrl();\n\n      await checkInternetConnection();\n\n      if (!hasInternet) {\n        loading = false;\n        return;\n      }\n\n      log('URL ${AppConstants.appUrl}');\n    } on Exception catch (e) {\n      log('Error $e');\n\n      loading = false;\n    }\n  }\n\n  static Future<void> addFcmTokenToUrl() async {\n    try {\n      final token = OneSignalNotificationService.getUserId();\n\n      AppConstants.appUrl += '?token=$token';\n    } on Exception catch (e) {\n      log('Error $e');\n    }\n  }\n\n  //? Check Internet Connection\n  Future<void> checkInternetConnection() async {\n    hasInternet = await InternetConnectionChecker().hasConnection;\n    notifyListeners();\n  }\n\n  InAppWebViewController? webViewController;\n\n  //? Force refresh WebView to get latest content\n  Future<void> forceRefresh() async {\n    if (webViewController != null) {\n      await webViewController!.reload();\n    }\n  }\n\n  //? with flutter_inappwebview package\n  Future<void> onWebViewCreated(InAppWebViewController controller) async {\n    try {\n      //? set loading\n      // loading = false;\n\n      //? set controller\n      webViewController = controller;\n\n      await addTokenToLogin(controller: controller);\n\n      notifyListeners();\n    } on TimeoutException catch (_) {\n      log('Timeout occurred');\n\n      loading = false;\n\n      Restart.restartApp();\n    } on Exception catch (e) {\n      log('Error $e');\n\n      loading = false;\n\n      Restart.restartApp();\n    }\n  }\n  Future<void> addTokenToLogin({\n    required InAppWebViewController controller,\n  }) async {\n    final currentUrl = await controller.getUrl();\n\n    final uri = Uri.parse(currentUrl.toString());\n    final tokenInUrl = uri.queryParameters['token'];\n\n    final shouldAddToken = (tokenInUrl == null || tokenInUrl.trim().isEmpty) &&\n        currentUrl.toString().contains('login');\n\n    if (shouldAddToken) {\n      Log.w('SHOULD ADD TOKEN------');\n      final token = OneSignalNotificationService.getUserId();\n      if (token.isEmpty) {\n        Future.delayed(const Duration(seconds: 5), () async {\n          await addTokenToLogin(controller: controller);\n        });\n      } else {\n        await controller.loadUrl(\n          urlRequest: URLRequest(\n            url: WebUri('${AppConstants.appUrl}?token=$token'),\n            // url: WebUri('${currentUrl.toString()}?token=$token'),\n          ),\n        );\n      }\n    }\n  }\n\n//? Add Token To Login\n  // Future<void> addTokenToLogin({\n  //   required InAppWebViewController controller,\n  // }) async {\n  //   final currentUrl = await controller.getUrl();\n  //\n  //   final shouldAddToken = currentUrl.toString() == AppConstants.appUrl &&\n  //       !currentUrl.toString().contains('token=');\n  //\n  //   if (shouldAddToken) {\n  //     Log.w('SHOULD ADD TOKEN------');\n  //     final token = OneSignalNotificationService.getUserId();\n  //     await controller.loadUrl(\n  //       urlRequest: URLRequest(\n  //         url: WebUri('${currentUrl.toString()}?token=$token'),\n  //       ),\n  //     );\n  //   }\n  // }\n}\n", "baseTimestamp": 1758012311919, "deltas": [{"timestamp": 1758012314169, "changes": [{"type": "INSERT", "lineNumber": 97, "content": "  "}]}]}}}